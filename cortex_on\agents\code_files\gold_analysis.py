# Initialize prices
current_price_22k = 9140
historical_prices_22k = [9140] * 7  # Same price for all 7 days
current_price_24k = 9971

# Calculate average and analysis
seven_day_avg = sum(historical_prices_22k) / len(historical_prices_22k)
price_stability = len(set(historical_prices_22k)) == 1  # True if all prices are the same

# Generate analysis report
print(f"Gold Price Analysis Report\n")
print(f"Current 22K Gold Price: ₹{current_price_22k}")
print(f"Current 24K Gold Price: ₹{current_price_24k}")
print(f"7-Day Average (22K): ₹{seven_day_avg}")
print(f"Price Stability: {'Stable' if price_stability else 'Volatile'}")
print("\nRecommendation:")
if price_stability:
    print("The gold price has been stable at ₹9140 for the past week.")
    print("This indicates a steady market without significant fluctuations.")
    print("Recommendation: NEUTRAL - Consider your investment goals and risk tolerance.")